import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Video,
  Zap,
  Shield,
  Check,
  TrendingUp,
  Target,
  Award,
  ChevronDown,
  ChevronUp,
  Flame,
  Rocket,
} from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SEO from "@/components/SEO";
import AnimatedRocket from '@/components/AnimatedRocket';
import UserLogin from '@/components/UserLogin';
import { useAuth } from '@/lib/AuthContext';
import { toast } from '@/components/ui/use-toast';
import authService from '@/lib/authService';
import user1 from '@/images/user-1.png';
import user2 from '@/images/user-2.jpg';
import user3 from '@/images/user-3.jpg';

const PremiumMembers = () => {
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [isCreatingCheckout, setIsCreatingCheckout] = useState(false);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [checkingSubscription, setCheckingSubscription] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Check subscription status when user is authenticated
  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (!isAuthenticated) {
        setHasActiveSubscription(false);
        return;
      }

      try {
        setCheckingSubscription(true);
        const token = authService.getUserToken();

        if (!token) return;

        const response = await fetch(`${import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000'}/api/v1/case-study-subscriptions/access`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          setHasActiveSubscription(data.success && data.data.hasAccess);
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
        setHasActiveSubscription(false);
      } finally {
        setCheckingSubscription(false);
      }
    };

    checkSubscriptionStatus();
  }, [isAuthenticated]);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const handleGetStartedClick = async () => {
    if (!isAuthenticated) {
      setShowLoginPopup(true);
      return;
    }

    // If user already has active subscription, redirect to case studies
    if (hasActiveSubscription) {
      toast({
        title: "Already Subscribed! 🎉",
        description: "You already have access to all premium case study content. Redirecting you now...",
        variant: "default"
      });

      setTimeout(() => {
        navigate('/case-studies');
      }, 1500);

      return;
    }

    // User is logged in and doesn't have subscription, proceed to checkout
    try {
      setIsCreatingCheckout(true);

      const token = authService.getUserToken();
      if (!token) {
        throw new Error('No authentication token found. Please login again.');
      }

      const response = await fetch(`${import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000'}api/v1/case-study-subscriptions/checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle authentication errors specifically
        if (response.status === 401) {
          throw new Error(data.error || 'Authentication failed');
        }
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe checkout
      window.location.href = data.url;
    } catch (error: any) {
      console.error('Checkout error:', error);

      let errorMessage = "Failed to start checkout process. Please try again.";

      // Check for specific error types
      if (error.message.includes('No authentication token found')) {
        errorMessage = "Please login again to continue.";
        // Optionally trigger login popup
        setShowLoginPopup(true);
      } else if (error.message.includes('User already has an active case study subscription')) {
        errorMessage = "Great news! You already have an active case study subscription. You can access all premium content right away.";
        // Show success style toast instead of error
        toast({
          title: "Already Subscribed! 🎉",
          description: "You already have access to all premium case study content. Start exploring now!",
          variant: "default"
        });

        // Redirect to case studies page after a short delay
        setTimeout(() => {
          navigate('/case-studies');
        }, 2000);

        return; // Exit early to avoid showing error toast
      } else if (error.message.includes('fetch') || error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
        errorMessage = "Backend server is not running. Please start the backend server first.";
      } else if (error.message.includes('Unexpected token') || error.message.includes('DOCTYPE')) {
        errorMessage = "Backend server is not configured properly. Please check your .env file.";
      } else if (error.message.includes('Authentication failed') || error.message.includes('jwt malformed')) {
        errorMessage = "Your session has expired. Please login again.";
        // Optionally trigger login popup
        setShowLoginPopup(true);
      }

      toast({
        title: "Checkout Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsCreatingCheckout(false);
    }
  };

  const handleJoinPremiumClick = () => {
    if (!isAuthenticated) {
      setShowLoginPopup(true);
      return;
    }

    // User is logged in, redirect to stories page
    navigate('/stories');
  };

  const content = [
    {
      color: "bg-blue-100 dark:bg-blue-900/30",
      icon: <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />,
      title: "Know what matters, skip what doesn't.",
      description:
        "Stop spinning your wheels on the wrong ideas. Discover what's actually working right now, and get the confidence to pursue something you truly believe in.",
      content: (
        <img
          src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=500&h=400&fit=crop"
          alt="Know what matters visualization"
          className="object-cover w-full h-full"
        />
      ),
    },
    {
      color: "bg-green-100 dark:bg-green-900/30",
      icon: <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />,
      title: "Land your first customers fast.",
      description:
        "Follow in the footsteps of real founders who went from zero to $10k+ MRR. See the real steps they took, so you can start earning faster.",
      content: (
        <img
          src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=500&h=400&fit=crop"
          alt="Know what matters visualization"
          className="object-cover w-full h-full"
        />
      ),
    },
    {
      color: "bg-purple-100 dark:bg-purple-900/30",
      icon: <Zap className="h-6 w-6 text-purple-600 dark:text-purple-400" />,
      title: "Don't just watch, start building.",
      description:
        "It's easy to get stuck on the sidelines. Get inspired, break through your doubts, and finally take action on a project you'll stick with.",
      content: (
        <img
          src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=500&h=400&fit=crop"
          alt="Know what matters visualization"
          className="object-cover w-full h-full"
        />
      ),
    },
    {
      color: "bg-orange-100 dark:bg-orange-900/30",
      icon: <Award className="h-6 w-6 text-orange-600 dark:text-orange-400" />,
      title: "Build a life you love.",
      description:
        "Wake up excited to work on your own terms. Set your own hours, work from anywhere, and aim for that $10k/month milestone and beyond.",
      content: (
        <img
          src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=500&h=400&fit=crop"
          alt="Know what matters visualization"
          className="object-cover w-full h-full"
        />
      ),
    },
  ];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <SEO
        title="Premium Members | Startup Stories"
        description="Exclusive content and features for premium members of Startup Stories."
        keywords="premium members, exclusive content, premium features"
      />
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-indigo-900 via-purple-900 to-indigo-800 text-white py-20">
          <div className="container mx-auto px-4 text-center">
            <Badge className="bg-white/20 text-white border-white/30 mb-6 text-sm px-4 py-2">
              Join 2,500+ Successful Entrepreneurs
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Imagine building a business that <br />
              <span className="text-yellow-300"> Earns $10K a month</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto text-white/90">
              Work for yourself, set your own hours, and choose where you want to be. There's never been a better moment to start something of your own.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button
                size="lg"
                onClick={() => {
                  window.location.href = '#early-access-pricing';
                }}
                className="bg-yellow-400 text-black hover:bg-yellow-300 text-lg px-8 py-6 flex items-center gap-2"
              >
                <AnimatedRocket />
                <span>Start Building Today</span>
              </Button>
            </div>
            <p className="text-white/70 text-lg">
              Access detailed case studies, and proven strategies from founders who've built successful startups from scratch.
            </p>
          </div>
        </section>

        {/* Enhanced Value Proposition Sections */}
        <section className="py-20 bg-gradient-to-b from-background to-muted/20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
                Why Successful Founders Choose Us
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Join thousands of entrepreneurs who've transformed their ideas into thriving businesses
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
              {content.map((item, index) => (
                <div
                  key={item.title + index}
                  className="group relative bg-card/50 backdrop-blur-sm border border-border/50 rounded-3xl p-8 hover:shadow-2xl hover:shadow-primary/10 transition-all duration-500 hover:-translate-y-2 hover:border-primary/20"
                >
                  {/* Background gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Content */}
                  <div className="relative z-10">
                    <div className="flex items-start gap-6 text-left">
                      <div className={`w-16 h-16 rounded-2xl ${item.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                        {item.icon}
                      </div>

                      <div className="flex-1">
                        <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                          {item.title}
                        </h3>
                        <p className="text-lg text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                          {item.description}
                        </p>
                      </div>
                    </div>

                    {/* Decorative element */}
                    <div className="absolute -bottom-2 -right-2 w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl" />
                  </div>

                  {/* Border glow effect */}
                  <div className="absolute inset-0 rounded-3xl border-2 border-transparent bg-gradient-to-r from-primary/20 via-transparent to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" style={{ padding: '2px' }}>
                    <div className="rounded-3xl bg-card h-full w-full" />
                  </div>
                </div>
              ))}
            </div>

          </div>
        </section>

       {/* Premium Access Section */}
<section className="py-16 bg-background">
  <div className="container mx-auto px-4 text-center">
    <h2 className="text-4xl font-bold mb-4 text-foreground">
      Unlock Everything with StartupStories Premium
    </h2>
    <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
      A membership designed to give founders real advantages, insights, and connections - so you can build faster and smarter.
    </p>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center bg-gradient-to-br from-blue-50/50 to-background dark:from-blue-950/20 dark:to-background rounded-2xl p-8 shadow-lg border border-blue-100 dark:border-blue-800">
      <div className="text-left">
        <h3 className="text-2xl font-bold mb-6 text-foreground">Case Studies Database</h3>
        <p className="text-muted-foreground mb-6">
          600+ Founder Journeys from Idea to $500M+ in Value.
        </p>
        <p className="text-muted-foreground mb-6">
          Stop learning from theory. These are real founders who built real companies with real results, and they're sharing exactly how they did it.
        </p>
        <p className="text-muted-foreground mb-8">
          Your competitive advantage: Advanced search lets you study founders who faced your exact challenges. Struggling with product-market fit? Browse detailed success stories. Need scaling strategies? Learn from 83 founders who've been there.
        </p>
        <p className="text-muted-foreground mb-0">
          Every case study includes real numbers, proven strategies, and expensive lessons already learned. Stop guessing. Start copying what actually works.
        </p>
      </div>
      <div className="flex justify-end">
        <img
          src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=500&h=400&fit=crop"
          alt="Case Studies Database"
          className="rounded-lg shadow-lg"
        />
      </div>
    </div>
  </div>
</section>


        {/* Early Access Pricing Section */}
        <section className="py-16 bg-background" id="early-access-pricing">
          <div className="container mx-auto px-4">
            <div className="via-pink-50/50 to-purple-50/50 dark:via-pink-950/20 dark:to-purple-950/20">
              <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="text-center mb-12">
                  <h1 className="text-5xl font-bold mb-4">
                    <span className="text-foreground">Early Access: Startup Stories Launch Offer  </span>
                  </h1>
                  <p className="text-muted-foreground">
                  You're not just getting access, you're becoming a founding member of the most valuable entrepreneur community ever created. 500+ founders who've built real companies with real exits worth $500M+.
                  </p>
                </div>

                {/* Main content container */}
                <div className="bg-card/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-border">
                  {/* Offer banner */}
                  <div className="text-center mb-8 p-6 bg-gradient-to-r from-primary/10 to-primary/20 rounded-2xl border border-primary/20">
                    <p className="text-foreground text-lg">
                      Early Bird Offer: <span className="font-bold text-primary">Save 33%!</span> Unlock full access to Startup Stories - only a few spots left!
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-12 items-start">
                    {/* Left side - Pricing */}
                    <div>
                      {/* Plan selector */}
                      <div className="flex mb-8">
                        <button className="flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-full font-semibold">
                          <Flame className="w-4 h-4" />
                          Founder's Annual Plan
                        </button>
                      </div>

                      {/* Pricing card */}
                      <div className="mb-6">
                        <h3 className="text-foreground text-lg mb-4">Limited offer annual plan</h3>
                        <div className="mb-4">
                          <span className="text-5xl font-bold text-foreground">$300/</span>
                          <span className="text-muted-foreground">year (normally $690)</span>
                        </div>
                        <p className="text-muted-foreground italic mb-6">That's just $16.41/month, billed annually.</p>

                        {/* Progress bar */}
                        <div className="mb-4">
                          <div className="bg-muted rounded-full h-3 mb-2">
                            <div className="bg-gradient-to-r from-primary/80 to-primary/80 h-3 rounded-full" style={{ width: '90%' }}></div>
                          </div>
                          <p className="text-primary font-semibold">80 / 100 left</p>
                        </div>

                        <p className="text-muted-foreground mb-2">Next price: <span className="font-semibold">$300</span></p> 

                        {hasActiveSubscription && (
                          <div className="bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg p-3 mb-4">
                            <p className="text-green-800 dark:text-green-300 text-sm font-medium flex items-center">
                              <Check className="h-4 w-4 mr-2" />
                              You're already subscribed! Access all premium content.
                            </p>
                          </div>
                        )}
                        <Button
                          size="lg"
                          className={`w-full px-8 py-6 text-base ${hasActiveSubscription ? 'bg-green-600 hover:bg-green-700' : ''}`}
                          onClick={handleGetStartedClick}
                          disabled={isCreatingCheckout || checkingSubscription}
                        >
                          <Rocket className="mr-2" style={{ width: '20px', height: '20px' }} />
                          {checkingSubscription
                            ? 'Checking Status...'
                            : isCreatingCheckout
                              ? 'Starting Checkout...'
                              : hasActiveSubscription
                                ? 'Access Your Content →'
                                : 'Get Started Today'
                          }
                        </Button>
                      </div>
                    </div>

                    {/* Right side - Features */}
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-6">What's Included:</h3>
                      <div className="space-y-4">
                        {[
                          { text: "500+ Million-Dollar Case Studies", subtitle: 'Real companies, real strategies, real results' },
                          { text: "Founding Member Badge", subtitle: 'Exclusive status in the most valuable startup community' },
                          { text: "Mastermind Sessions", subtitle: 'Live Q&A with founders who ve raised millions' },
                          { text: "1 to 1 Mentorship", subtitle: 'Talk with Startup Stories mentors every month for real advice and support' },
                        ].map((feature, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <div className="bg-primary/10 rounded-full p-0.5 mr-3 mt-0.5">
                              <Check className="h-4 w-4 text-primary flex-shrink-0" />
                            </div>
                            <div>
                              <p className="font-bold text-foreground">{feature.text}</p>
                              <p className="text-muted-foreground text-sm">{feature.subtitle}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className='w-full flex justify-center mt-8'>
                    <div>
                      <h2 className="text-center text-xl font-bold text-foreground">What's Included</h2>
                      <p className="text-muted-foreground">
                        This offer is only for early believers. Once the counter hits 100, it's gone forever.
                        🕐 80/100 slots already claimed
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Trust & Social Proof */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-foreground">Trusted by Successful Entrepreneurs</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Our members have raised $50M+ in funding and built companies valued at over $200M
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="text-center p-6 bg-card border-border">
                <CardContent>
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden">
                    <img src={user1} alt="Member" className="w-full h-full object-cover" />
                  </div>
                  <p className="text-muted-foreground mb-4">
                    "Startup Stories Premium gave me insider access to case studies and playbooks I couldn't find anywhere else. I used the founder lessons to land my first 20 paying customers."
                  </p>
                  <div className="font-semibold text-foreground">Sarah M.</div>
                  <div className="text-sm text-muted-foreground">SaaS Founder</div>
                </CardContent>
              </Card>

              <Card className="text-center p-6 bg-card border-border">
                <CardContent>
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden">
                    <img src={user2} alt="Member" className="w-full h-full object-cover" />
                  </div>
                  <p className="text-muted-foreground mb-4">
                    "Being able to connect with other verified founders in the Premium community made all the difference. I got honest feedback on my product and even found a co-founder."
                  </p>
                  <div className="font-semibold text-foreground">Lisa T.</div>
                  <div className="text-sm text-muted-foreground">Startup Stories Premium Member</div>
                </CardContent>
              </Card>

              <Card className="text-center p-6 bg-card border-border">
                <CardContent>
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden">
                    <img src={user3} alt="Member" className="w-full h-full object-cover" />
                  </div>
                  <p className="text-muted-foreground mb-4">
                    "Startup Stories Premium is worth every penny. The unlimited access to expert content, early community perks, and actionable advice have completely changed how I approach building my business."
                  </p>
                  <div className="font-semibold text-foreground">Emily C.</div>
                  <div className="text-sm text-muted-foreground">Digital Entrepreneur</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Premium Features */}
        {/* Premium Features */}
<section className="py-16 bg-background">
  <div className="container mx-auto px-4">
    <div className="text-center mb-16">
      <h2 className="text-4xl font-bold mb-4 text-foreground">What's Inside Premium Membership</h2>
      <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
        Get exclusive access to content, tools, and community that you won't find anywhere else
      </p>
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
      {/* Card 1 - Startup Secrets Library */}
      <Card className="p-8 border-2 border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-950/30">
        <CardContent className='md:p-6 md:p-0 p-0'>
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-green-600 dark:bg-green-500 rounded-lg flex items-center justify-center mr-4">
              <Users className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-foreground">The Startup Secrets Library</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-start">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-1" />
              <div>
                <p className="font-semibold text-foreground">Case Study Breakdowns</p>
                <p className="text-muted-foreground">The exact playbooks 500+ unicorn startups used to scale from $0 to $100M</p>
              </div>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-1" />
              <div>
                <p className="font-semibold text-foreground">Behind-the-Scenes Stories</p>
                <p className="text-muted-foreground">Raw failure stories and breakthrough moments from founders who've been there</p>
              </div>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-1" />
              <div>
                <p className="font-semibold text-foreground">Revenue & Marketing Playbooks</p>
                <p className="text-muted-foreground">Step-by-step systems that generated $2.3B+ in member revenue and landed first 1,000 customers</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card 2 - Founder Network + Mentorship */}
      <Card className="p-8 border-2 border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-950/30">
        <CardContent className='md:p-6 md:p-0 p-0'>
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-orange-600 dark:bg-orange-500 rounded-lg flex items-center justify-center mr-4">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-foreground">Elite Founder Network + Mentorship</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-start">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-1" />
              <div>
                <p className="font-semibold text-foreground">1-on-1 Mentorship Calls</p>
                <p className="text-muted-foreground">Monthly sessions with Startup Story mentors who've built and sold companies.</p>
              </div>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-1" />
              <div>
                <p className="font-semibold text-foreground">Virtual Meetups</p>
                <p className="text-muted-foreground">Network with founders who've collectively raised and built real business</p>
              </div>
            </div>
            <div className="flex items-start">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-1" />
              <div>
                <p className="font-semibold text-foreground">Strategy & Launch Feedback</p>
                <p className="text-muted-foreground">Brutally honest feedback on your business ideas from founders who've been there</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
            {/* Value Proposition Section */}
            <Card className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-8">
              <CardContent className="text-center md:p-6 md:p-0 p-0">
                <h3 className="md:text-3xl text-2xl font-bold mb-6">
                  Unlock Premium Stories, Case Studies & Insider Playbooks
                </h3>
                <p className="text-xl">
                  Every scroll you just made? That's another founder getting ahead while you're stuck reading introductions.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                </div>
                <div className="mt-3 space-y-4">
                  <p>500+ founders went from where you are to building $500M+ in value. Their secret? They stopped learning alone and started learning from others who'd already won.</p>

                  <p className="font-semibold">What you're missing:</p>
                  <ul className="text-left max-w-lg mx-auto space-y-2 mb-4">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 mr-3 mt-1 text-yellow-300" />
                      <span>The exact playbooks that built $500M+ in startup value</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 mr-3 mt-1 text-yellow-300" />
                      <span>Case studies with real numbers, real strategies, real results</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 mr-3 mt-1 text-yellow-300" />
                      <span>Battle-tested frameworks from founders who've actually exited</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 mr-3 mt-1 text-yellow-300" />
                      <span>A community of entrepreneurs who've been exactly where you are</span>
                    </li>
                  </ul>

                  <p>For just $197/year (less than $4/week), stop learning the expensive way and start learning from those who've already won.</p>
                  <p className="font-semibold text-yellow-300">Every day you delay is another costly mistake your competitors won't make.</p>
                </div>
              </CardContent>
            </Card>
  </div>
</section>


        {/* FAQ Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4 text-foreground">Frequently Asked Questions</h2>
              <p className="text-xl text-muted-foreground">Get answers to common questions</p>
            </div>

            <div className="max-w-4xl mx-auto space-y-4">
              {[
                {
                  question: "What do I get with Startup Stories Premium?",
                  answer: "Premium members get unlimited access to all exclusive case studies, founder playbooks, step-by-step business breakdowns, and member-only stories. You'll also enjoy a premium founder profile, early access to new features, and a supportive private community."
                },
                {
                  question: "Can I try Premium before paying?",
                  answer: "We offer previews and free stories so you can see the value firsthand."
                },
                {
                  question: "Can I cancel my Premium plan anytime?",
                  answer: "Yes, you can cancel your renewal at any time from your account settings. Your access will continue until the end of your paid period."
                },
                {
                  question: "Will my story be included in Premium content?",
                  answer: "Yes, user-submitted stories may be featured as part of our premium offering, always credited to your name or profile."
                },
                {
                  question: "Can I update or remove my story after it's published?",
                  answer: "Yes! You can contact us <NAME_EMAIL> to update or request removal of your content. You can also, login with your credential and do it at your own end."
                },
                {
                  question: "Who should join the Premium plan?",
                  answer: "Premium is perfect for founders, indie hackers, and entrepreneurs who want actionable insights, access to successful founder journeys, and a shortcut to launching and growing their own business."
                }
              ].map((faq, index) => (
                <Card key={index} className="border border-border bg-card">
                  <CardContent className="p-0">
                    <button
                      className="w-full text-left p-6 flex items-center justify-between hover:bg-muted/50 transition-colors"
                      onClick={() => toggleFaq(index)}
                    >
                      <span className="font-semibold text-lg text-foreground">{faq.question}</span>
                      {openFaq === index ? (
                        <ChevronUp className="h-5 w-5 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-muted-foreground" />
                      )}
                    </button>
                    {openFaq === index && (
                      <div className="px-6 pb-6">
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-4xl font-bold mb-4">Start Building Your Startup Story Today</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Get access to real founder journeys, proven strategies, and a community that's ready to help you grow. Sign up and discover what you can build when you're surrounded by people who've actually done it.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button size="lg" className="bg-yellow-400 text-black hover:bg-yellow-300 text-lg px-8 py-6 flex items-center gap-2" onClick={handleJoinPremiumClick}>
                <AnimatedRocket />
                <span>Join Startup Stories Premium</span>
              </Button>
            </div>
            <div className="flex flex-wrap justify-center items-center gap-8 text-white/80">
              <div className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                SSL Secured
              </div>
              <div className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                2,500+ Members
              </div>
              <div className="flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Build you Business
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />

      <UserLogin
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        preventScroll={true}
        blurBackground={true}
      />
    </div>
  );
};

export default PremiumMembers;
